import AsyncStorage from '@react-native-async-storage/async-storage';
import NetInfo from '@react-native-community/netinfo';
import { supabase } from './supabase';

export interface OfflineFlashcard {
  id: string;
  front: string;
  back: string;
  category: string;
  difficulty: 'easy' | 'medium' | 'hard';
  next_review_date: string;
  review_count: number;
  ease_factor: number;
  interval_days: number;
  user_id: string;
  last_synced?: string;
}

export interface OfflineStudySession {
  id: string;
  user_id: string;
  session_type: string;
  duration_minutes: number;
  completed_at: string;
  performance_score: number | null;
  synced: boolean;
}

export interface OfflineAction {
  id: string;
  type: 'UPDATE_FLASHCARD' | 'CREATE_SESSION' | 'DELETE_FLASHCARD';
  data: any;
  timestamp: string;
  synced: boolean;
}

class OfflineService {
  private static instance: OfflineService;
  private isOnline: boolean = true;
  private syncInProgress: boolean = false;

  static getInstance(): OfflineService {
    if (!OfflineService.instance) {
      OfflineService.instance = new OfflineService();
    }
    return OfflineService.instance;
  }

  async initialize(): Promise<void> {
    // Monitor network connectivity
    NetInfo.addEventListener(state => {
      const wasOffline = !this.isOnline;
      this.isOnline = state.isConnected ?? false;
      
      // If we just came back online, trigger sync
      if (wasOffline && this.isOnline) {
        this.syncOfflineData();
      }
    });

    // Get initial network state
    const netInfo = await NetInfo.fetch();
    this.isOnline = netInfo.isConnected ?? false;
  }

  isConnected(): boolean {
    return this.isOnline;
  }

  // Cache flashcards for offline use
  async cacheFlashcards(flashcards: OfflineFlashcard[]): Promise<void> {
    try {
      const cacheData = {
        flashcards,
        lastUpdated: new Date().toISOString(),
      };
      
      await AsyncStorage.setItem('cached_flashcards', JSON.stringify(cacheData));
    } catch (error) {
      console.error('Error caching flashcards:', error);
    }
  }

  // Get cached flashcards
  async getCachedFlashcards(): Promise<OfflineFlashcard[]> {
    try {
      const cached = await AsyncStorage.getItem('cached_flashcards');
      if (cached) {
        const data = JSON.parse(cached);
        return data.flashcards || [];
      }
      return [];
    } catch (error) {
      console.error('Error getting cached flashcards:', error);
      return [];
    }
  }

  // Get due flashcards (works offline)
  async getDueFlashcards(): Promise<OfflineFlashcard[]> {
    const flashcards = await this.getCachedFlashcards();
    const now = new Date();
    
    return flashcards.filter(card => 
      new Date(card.next_review_date) <= now
    );
  }

  // Update flashcard (works offline)
  async updateFlashcard(
    cardId: string, 
    updates: Partial<OfflineFlashcard>
  ): Promise<void> {
    try {
      // Update in cache
      const flashcards = await this.getCachedFlashcards();
      const cardIndex = flashcards.findIndex(card => card.id === cardId);
      
      if (cardIndex !== -1) {
        flashcards[cardIndex] = { ...flashcards[cardIndex], ...updates };
        await this.cacheFlashcards(flashcards);
      }

      // Queue for sync if offline
      if (!this.isOnline) {
        await this.queueOfflineAction({
          id: `update_${cardId}_${Date.now()}`,
          type: 'UPDATE_FLASHCARD',
          data: { cardId, updates },
          timestamp: new Date().toISOString(),
          synced: false,
        });
      } else {
        // Sync immediately if online
        await this.syncFlashcardUpdate(cardId, updates);
      }
    } catch (error) {
      console.error('Error updating flashcard:', error);
    }
  }

  // Record study session (works offline)
  async recordStudySession(session: Omit<OfflineStudySession, 'id' | 'synced'>): Promise<void> {
    try {
      const sessionWithId: OfflineStudySession = {
        ...session,
        id: `session_${Date.now()}`,
        synced: false,
      };

      // Store session locally
      const sessions = await this.getOfflineStudySessions();
      sessions.push(sessionWithId);
      await AsyncStorage.setItem('offline_sessions', JSON.stringify(sessions));

      // Queue for sync if offline
      if (!this.isOnline) {
        await this.queueOfflineAction({
          id: `session_${sessionWithId.id}`,
          type: 'CREATE_SESSION',
          data: sessionWithId,
          timestamp: new Date().toISOString(),
          synced: false,
        });
      } else {
        // Sync immediately if online
        await this.syncStudySession(sessionWithId);
      }
    } catch (error) {
      console.error('Error recording study session:', error);
    }
  }

  // Get offline study sessions
  async getOfflineStudySessions(): Promise<OfflineStudySession[]> {
    try {
      const sessions = await AsyncStorage.getItem('offline_sessions');
      return sessions ? JSON.parse(sessions) : [];
    } catch (error) {
      console.error('Error getting offline sessions:', error);
      return [];
    }
  }

  // Queue offline action
  private async queueOfflineAction(action: OfflineAction): Promise<void> {
    try {
      const actions = await this.getOfflineActions();
      actions.push(action);
      await AsyncStorage.setItem('offline_actions', JSON.stringify(actions));
    } catch (error) {
      console.error('Error queueing offline action:', error);
    }
  }

  // Get offline actions
  private async getOfflineActions(): Promise<OfflineAction[]> {
    try {
      const actions = await AsyncStorage.getItem('offline_actions');
      return actions ? JSON.parse(actions) : [];
    } catch (error) {
      console.error('Error getting offline actions:', error);
      return [];
    }
  }

  // Sync offline data when connection is restored
  async syncOfflineData(): Promise<void> {
    if (this.syncInProgress || !this.isOnline) {
      return;
    }

    this.syncInProgress = true;

    try {
      const actions = await this.getOfflineActions();
      const unsyncedActions = actions.filter(action => !action.synced);

      for (const action of unsyncedActions) {
        try {
          switch (action.type) {
            case 'UPDATE_FLASHCARD':
              await this.syncFlashcardUpdate(action.data.cardId, action.data.updates);
              break;
            case 'CREATE_SESSION':
              await this.syncStudySession(action.data);
              break;
            case 'DELETE_FLASHCARD':
              await this.syncFlashcardDeletion(action.data.cardId);
              break;
          }

          // Mark as synced
          action.synced = true;
        } catch (error) {
          console.error('Error syncing action:', action, error);
        }
      }

      // Update actions list
      await AsyncStorage.setItem('offline_actions', JSON.stringify(actions));

      // Clean up old synced actions (keep last 100)
      const syncedActions = actions.filter(action => action.synced);
      if (syncedActions.length > 100) {
        const recentActions = actions.slice(-100);
        await AsyncStorage.setItem('offline_actions', JSON.stringify(recentActions));
      }

    } catch (error) {
      console.error('Error syncing offline data:', error);
    } finally {
      this.syncInProgress = false;
    }
  }

  // Sync individual flashcard update
  private async syncFlashcardUpdate(cardId: string, updates: Partial<OfflineFlashcard>): Promise<void> {
    try {
      const { error } = await supabase
        .from('flashcards')
        .update(updates)
        .eq('id', cardId);

      if (error) {
        throw error;
      }
    } catch (error) {
      console.error('Error syncing flashcard update:', error);
      throw error;
    }
  }

  // Sync study session
  private async syncStudySession(session: OfflineStudySession): Promise<void> {
    try {
      const { error } = await supabase
        .from('study_sessions')
        .insert({
          user_id: session.user_id,
          session_type: session.session_type,
          duration_minutes: session.duration_minutes,
          completed_at: session.completed_at,
          performance_score: session.performance_score,
        });

      if (error) {
        throw error;
      }

      // Mark as synced in local storage
      const sessions = await this.getOfflineStudySessions();
      const sessionIndex = sessions.findIndex(s => s.id === session.id);
      if (sessionIndex !== -1) {
        sessions[sessionIndex].synced = true;
        await AsyncStorage.setItem('offline_sessions', JSON.stringify(sessions));
      }
    } catch (error) {
      console.error('Error syncing study session:', error);
      throw error;
    }
  }

  // Sync flashcard deletion
  private async syncFlashcardDeletion(cardId: string): Promise<void> {
    try {
      const { error } = await supabase
        .from('flashcards')
        .delete()
        .eq('id', cardId);

      if (error) {
        throw error;
      }
    } catch (error) {
      console.error('Error syncing flashcard deletion:', error);
      throw error;
    }
  }

  // Get sync status
  async getSyncStatus(): Promise<{
    pendingActions: number;
    lastSyncTime: string | null;
    isOnline: boolean;
  }> {
    const actions = await this.getOfflineActions();
    const pendingActions = actions.filter(action => !action.synced).length;
    
    const lastSyncTime = await AsyncStorage.getItem('last_sync_time');
    
    return {
      pendingActions,
      lastSyncTime,
      isOnline: this.isOnline,
    };
  }

  // Force sync
  async forceSync(): Promise<void> {
    if (!this.isOnline) {
      throw new Error('Cannot sync while offline');
    }

    await this.syncOfflineData();
    await AsyncStorage.setItem('last_sync_time', new Date().toISOString());
  }

  // Clear all offline data (for logout)
  async clearOfflineData(): Promise<void> {
    try {
      await Promise.all([
        AsyncStorage.removeItem('cached_flashcards'),
        AsyncStorage.removeItem('offline_sessions'),
        AsyncStorage.removeItem('offline_actions'),
        AsyncStorage.removeItem('last_sync_time'),
      ]);
    } catch (error) {
      console.error('Error clearing offline data:', error);
    }
  }
}

export const offlineService = OfflineService.getInstance();
